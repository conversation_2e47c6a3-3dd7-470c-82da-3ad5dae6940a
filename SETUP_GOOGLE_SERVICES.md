# Fix Google Services JSON Configuration

## Issue
The EAS build is failing because it cannot find the `google-services.json` file.

## Solution

### Step 1: Download your actual google-services.json
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Click the gear icon (Project Settings)
4. Under "Your apps" section, find your Android app
5. Click "Download google-services.json"

### Step 2: Update the EAS environment variable
Instead of using a file path, you need to upload the actual content of your google-services.json file:

```bash
# First, update the EAS CLI to latest version
npm install -g eas-cli

# Then update your environment variable with the actual file content
eas env:update --name GOOGLE_SERVICES_JSON --value "$(cat google-services.json)"
```

### Step 3: Verify the configuration
Your eas.json has been updated to properly reference the environment variable.

### Step 4: Test the build
```bash
eas build --platform android --profile production
```

## Important Notes
- The google-services.json file should NOT be committed to git (it's already in .gitignore)
- The EAS environment variable will provide the file content during build
- Make sure your Firebase project is properly configured for the Android package name: `com.barisimdt.Giftmi`